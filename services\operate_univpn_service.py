import pathlib
import time
import os
import psutil
import subprocess
import pyautogui
import threading

from PyQt5 import QtCore
from common.Utils.StringUtils import StringUtils
from pywinauto.application import Application


class OperateUniVpn:

    def __init__(self, path, isDebug, signalUpdateProgress, parent):
        """
        控制uniVpn
        """
        self.path = path
        self.parent = parent
        self.isDebug = isDebug
        self.signalUpdateProgress = signalUpdateProgress
        self.isPathExist()
        self.app = None
        self.pywinautoPath = pathlib.Path("./config/pywinauto").resolve()

    def isPathExist(self):
        """
        判断path是否存在
        :return:
        """
        path = pathlib.Path(self.path)
        if not path.exists():
            raise Exception("数字财政VPN工具路径有误")

    def checkUniVpn(self):
        """
        检查uniVpn进程是否正在运行
        :return:
        """
        try:
            # 获取当前正在运行的进程列表
            process_list = psutil.process_iter()
            for process in process_list:
                if process.name() == "UniVPN.exe":
                    print("Process is running.")
                    return process
            else:
                print("Process is not running.")
                return False
        except Exception as err:
            raise Exception("检查uniVpn进程时出错, {}".format(str(err).strip()))

    def openUniVpn(self):
        """
        启动uniVpn进程
        :return:
        """
        try:
            # 1 subprocess.call/.Popen
            subprocess.call([self.path], shell=True, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            # subprocess.Popen(self.path, shell=True)
            # 2 app = Application(backend="uia").start(path)
            # os.system(r'"D:\Yoke\微易\任务\20230815凭证对比软件\凭证对比工具安装包\2023-11-11\pzdb.exe"')
            # 3 QtCore.QProcess
            # process = QtCore.QProcess()
            # process.start(r"C:\\Program Files (x86)\\UniVPN\\UniVpn.exe", [])
            # if not process.waitForStarted():
            #     print("Failed to start the external program.")
            #     return
            # process.waitForFinished()

            print("启动uniVpn成功, Program started!")
        except FileNotFoundError:
            print("启动uniVpn进程时出错, File not found.")
        except OSError:
            print("启动uniVpn进程时出错, OS error occurred.")
        except Exception as err:
            raise Exception("启动uniVpn进程时出错, {}".format(str(err).strip()))

    def closeUniVpn(self, process):
        """
        结束uniVpn进程
        :param process: 进程对象
        :return:
        """
        try:
            # 结束进程
            process.terminate()
            print("Process is killed")
        except Exception as err:
            raise Exception("结束uniVpn进程时出错, {}".format(str(err).strip()))

    def checkCondition(self):
        """
        检查是否有警告框
        :return:
        """
        self.app = Application(backend="uia").connect(path=self.path)
        # 警告窗1
        try:
            dlg_warn = self.app["警告"]
            dlg_warn.print_control_identifiers(filename=self.pywinautoPath)
            with open(self.pywinautoPath, 'rt', encoding="gbk") as f:
                f.seek(0)  # 移动文件指针
                str_ctrl = f.read()
            content = self.getStaticContent(str_ctrl)
            self.emitSignalUpdateProgress("成功获取窗口内容: {}".format(content))
            dlg_warn['确定 Enter'].click()  # 发现警告窗口，就点击确定
            self.emitSignalUpdateProgress("点击警告框确定 框信息: {}".format(content))
            return True
        except Exception as err1:
            self.emitSignalUpdateProgress("未找到警告确认框1, {}".format(str(err1).strip()))
            time.sleep(1)
        # 警告窗2
        try:
            dlg_warn2 = self.app["警告"]
            dlg_warn2.print_control_identifiers(filename=self.pywinautoPath)
            with open(self.pywinautoPath, 'rt', encoding="gbk") as f:
                f.seek(0)  # 移动文件指针
                str_ctrl = f.read()
            content = self.getStaticContent(str_ctrl)
            self.emitSignalUpdateProgress("成功获取窗口内容: {}".format(content))
            dlg_warn2['继续 Enter'].click()  # 发现警告窗口，就点击确定
            self.signalUpdateProgress.emit("点击警告框确定 框信息: {}".format(content))
            return True
        except Exception as e:
            self.emitSignalUpdateProgress("未找到警告确定框2".format(str(e).strip()))
            time.sleep(1)
        # 警告窗3
        try:
            dlg_link = self.app["SecoClient"]
            dlg_link.child_window(title="连接 Enter", control_type="Button").click()  # 发现secoClient窗口，就点击连接
            self.signalUpdateProgress.emit("点击secoClient框连接")
            return True
        except Exception as e:
            self.emitSignalUpdateProgress("未找到secoClient框3".format(str(e).strip()))
            time.sleep(1)
        # 警告窗4
        try:
            dlg_link2 = self.app["SecoClient"]
            dlg_link2.child_window(title="连接", control_type="Button").click()  # 发现secoClient窗口，就点击连接
            self.signalUpdateProgress.emit("点击secoClient框连接")
            return True
        except Exception as e:
            self.emitSignalUpdateProgress("未找到secoClient框4".format(str(e).strip()))
            time.sleep(1)
        # 警告窗5
        try:
            win_log = self.app["SecoClient"]
            dlg_log = win_log.child_window(title="登录", control_type="Window")
            dlg_log.child_window(title="登录 Enter", control_type="Button").click()  # 发现secoClient登录窗口，就点击登录
            self.signalUpdateProgress.emit("点击登录框登录")
            return True
        except Exception as e:
            self.emitSignalUpdateProgress("未找到secoClient框5".format(str(e).strip()))
        pyautogui.press('enter')    # 回车
        # raise Exception("未找到所有警告确定框")
        return False

    def restartUniVpn(self):
        """
        重启uniVpn进程
        :return:
        """
        process = self.checkUniVpn()
        if process:
            self.closeUniVpn(process)
        # self.openUniVpn()
        threading.Thread(target=self.openUniVpn).start()
        time.sleep(3)
        try_times = 0
        while try_times < 10 and not self.checkUniVpn():
            try_times += 1
            time.sleep(2)   # 循环等待
        if try_times >= 10:
            raise Exception("数字财政VPN进程未成功启动")
        else:
            self.emitSignalUpdateProgress("数字财政VPN进程已启动")
            if not self.checkCondition():
                self.emitSignalUpdateProgress("未找到所有警告确定框")

    def emitSignalUpdateProgress(self, message):
        """
        更新进度信息
        :param message: 进度信息
        :return:
        """
        if not self.isDebug:
            return
        self.signalUpdateProgress.emit(message)

    def getStaticContent(self, str_ctrl):
        """
        获取静态文本内容
        """
        start_index = 0
        start = str_ctrl.index("Static - '", start_index)
        while start >= 0:
            end = str_ctrl.index("'", start + len("Static - '"))
            content = str_ctrl[start + len("Static - '"):end]
            if StringUtils.isNotEmpty(str(content).strip()):
                return content
            start = str_ctrl.index("Static - '", end)
        return ""

