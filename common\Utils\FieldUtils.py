class FileUtils:

    @staticmethod
    def read(file_name, mode="rt"):
        '''
        读取文件内容，返回文件内容，类型为字符串，若文件不存在，则返回空
        ：prarm file_name : 文件名
                mode : 打开模式，常用方式 r 或 rb
                    r : 以读方式打开，只能读文件
                    rb : 以二进制读方式打开，只能读文件
                    rt : 以文本读方式打开，只能读文件
                    rb+ : 以二进制方式打开，可以读写文件
        '''
        try:
            with open(file_name, mode, encoding="utf-8") as f:
                f.seek(0)  # 移动文件指针
                content = f.read()
                return content
        except Exception as e:
            print(e)

    @staticmethod
    def saveFile(file_name, saveData, mode="wb", isEncode=False):
        '''
        读取文件内容，返回文件内容，类型为字符串，若文件不存在，则返回空
        ：prarm file_name : 文件名
                mode : 打开模式，常用方式 r 或 rb
                    r : 以读方式打开，只能读文件
                    rb : 以二进制读方式打开，只能读文件
                    rt : 以文本读方式打开，只能读文件
                    rb+ : 以二进制方式打开，可以读写文件
        '''
        try:
            if isEncode:
                with open(file_name, mode, encoding="utf-8") as f:
                    f.write(saveData)
            else:
                with open(file_name, mode) as f:
                    f.write(saveData)

        except Exception as e:
            print(e)

    @staticmethod
    def readByte(file_name):
        '''
        读取文件内容，返回文件内容，类型为字符串，若文件不存在，则返回空
        ：prarm file_name : 文件名
                mode : 打开模式，常用方式 r 或 rb
                    r : 以读方式打开，只能读文件
                    rb : 以二进制读方式打开，只能读文件
                    rt : 以文本读方式打开，只能读文件
                    rb+ : 以二进制方式打开，可以读写文件
        '''
        try:
            with open(file_name, 'rb') as f:
                content = f.read()
                return content
        except Exception as e:
            print(e)

    @staticmethod
    def fileOpen(file_name):
        txtstream = []
        with open(file_name, 'rb') as f:
            a = f.read()
        for i in a:
            txtstream.append(i)
        return txtstream
