import pathlib
import random
import time

import pyautogui
from PyQt5.QtCore import *
from pywinauto.application import Application
from ctypes import *

from common.Utils.StringUtils import StringUtils
from services.szcz_connector_service import SzczConnector
from services.operate_univpn_service import OperateUniVpn


class AutoConnectVpnNewThread(QThread):
    """
    报告状态线程类
    """
    # 更新进度信号
    signalUpdateProgress = pyqtSignal(str)  # str:进度信息

    def __init__(self, path, test_url, is_debug):
        # 设置线程初始工作状态
        super(AutoConnectVpnNewThread, self).__init__()
        self.stop = False
        self.path = path
        self.testUrl = test_url
        self.isDebug = is_debug
        self.app = None
        self.pywinautoPath = pathlib.Path("./config/pywinauto").resolve()
        self.pngAutoVpnList = [
            {
                "title": "强制下线警告框",
                "dialog_png_path": "./auto_vpn/png/01强制下线.png",
                "button_png_path": "./auto_vpn/png/01强制下线_确定.png",
                "button_name": "确定",
            },
            {
                "title": "网卡异常警告框",
                "dialog_png_path": "./auto_vpn/png/02网卡异常.png",
                "button_png_path": "./auto_vpn/png/02网卡异常_确定.png",
                "button_name": "确定",
            },
            {
                "title": "SecoClient连接框",
                "dialog_png_path": "./auto_vpn/png/03SecoClient标题.png",
                "button_png_path": "./auto_vpn/png/03SecoClient标题_连接.png",
                "button_name": "连接",
            },
            {
                "title": "登录确认框",
                "dialog_png_path": "./auto_vpn/png/04登录确认.png",
                "button_png_path": "./auto_vpn/png/04登录确认_登录.png",
                "button_name": "登录",
            },
            {
                "title": "安全警告框",
                "dialog_png_path": "./auto_vpn/png/05安全警告.png",
                "button_png_path": "./auto_vpn/png/05安全警告_继续.png",
                "button_name": "继续",
            },
            {
                "title": "连接断开警告框",
                "dialog_png_path": "./auto_vpn/png/06连接断开.png",
                "button_png_path": "./auto_vpn/png/06连接断开_确定.png",
                "button_name": "确定",
            },
        ]
        if not pathlib.Path(path).exists():
            self.stop = True  # 停止自动重连VPN
            self.signalUpdateProgress.emit("数字财政VPN工具路径有误 请确认是否选择正确 目前不会自动重连VPN")

    def __del__(self):
        # 线程状态改变与线程终止
        pass

    def run(self):
        """
        线程相关的执行代码
        :return:
        """
        while not self.stop:
            try:
                self.emitSignalUpdateProgress("================================================")
                szcz_connector = SzczConnector(self.testUrl)
                # # 1循环检测vpn连接状态 1-3分钟/2次 停顿2s
                is_conn, err_msg = szcz_connector.is_vpn_connected(try_time=1)
                if is_conn:
                    self.signalUpdateProgress.emit("检测vpn连接正常")
                else:
                    self.emitSignalUpdateProgress(err_msg)
                    operateUnivpn = OperateUniVpn(self.path, self.isDebug, self.signalUpdateProgress, self)
                    # # 2检测控件提示窗
                    self.emitSignalUpdateProgress("检测控件提示窗")
                    try:
                        operateUnivpn.checkCondition()
                        time.sleep(3)
                        is_conn, err_msg = szcz_connector.is_vpn_connected()
                        if is_conn:
                            self.emitSignalUpdateProgress("检测vpn连接正常")
                            return
                    except Exception as con_err:
                        if "not found" in str(con_err):
                            self.emitSignalUpdateProgress("数字财政VPN工具未启动")
                        else:
                            self.emitSignalUpdateProgress("控件执行失败, {}".format(str(con_err).strip()))

                    # # 3重启vpn（检测vpn进程是否存在|启动vpn|关闭vpn进程）
                    self.emitSignalUpdateProgress("正在重启数字财政vpn")
                    try:
                        operateUnivpn.restartUniVpn()
                        is_conn, err_msg = szcz_connector.is_vpn_connected(try_time=30)
                        if is_conn:
                            self.signalUpdateProgress.emit("数字财政vpn重启成功")
                        else:
                            raise Exception("重启后连接数字财政失败, {}".format(err_msg))
                    except Exception as uni_err:
                        raise Exception("重启数字财政vpn有误, {}".format(str(uni_err).strip()))
            except Exception as eOut:
                self.signalUpdateProgress.emit(str(eOut).strip())
            finally:
                sleep_time = random.randint(10, 1 * 60)
                self.emitSignalUpdateProgress("【提示】{}秒后将触发检测数字财政vpn连接状态".format(sleep_time))
                time.sleep(sleep_time)

    def update(self, path, test_url, is_debug):
        """
        更新配置
        :param path: VPN程序路径
        :param test_url: 测试数字财政连接地址
        :param is_debug: 是否调试
        :return:
        """
        self.isDebug = is_debug
        if StringUtils.isNotEmpty(path):
            self.path = path
        if StringUtils.isNotEmpty(test_url):
            self.testUrl = test_url

    def getStaticContent(self, str_ctrl):
        """
        获取静态文本内容
        """
        start_index = 0
        start = str_ctrl.index("Static - '", start_index)
        while start >= 0:
            end = str_ctrl.index("'", start + len("Static - '"))
            content = str_ctrl[start + len("Static - '"):end]
            if StringUtils.isNotEmpty(str(content).strip()):
                return content
            start = str_ctrl.index("Static - '", end)
        return ""

    def screenShot(self, image_name):
        """
        截屏
        :param image_name: 图片名称
        :return:
        """
        try:
            if not self.isDebug:
                return
            u = windll.LoadLibrary('user32.dll')
            if not u.GetForegroundWindow():  # 如果锁屏不访问界面
                return
            # 截取屏幕
            time_str = "{}{}".format(
                time.strftime("%Y-%m-%d %H%M%S", time.localtime()),
                random.randint(100, 999)
            )
            image_file_path = "./screen_auto_vpn/{} {}.png".format(
                time_str,
                image_name
            )
            screen_size = pyautogui.size()
            pyautogui.screenshot(
                imageFilename=image_file_path,
                region=(
                    screen_size.width / 4,
                    screen_size.height / 4,
                    screen_size.width / 2,
                    screen_size.height / 2
                )
            )
        except Exception as eSS:
            self.emitSignalUpdateProgress(str(eSS).strip())

    def emitSignalUpdateProgress(self, message):
        """
        更新进度信息
        :param message: 进度信息
        :return:
        """
        if not self.isDebug:
            return
        if StringUtils.isEmpty(message):
            return
        self.signalUpdateProgress.emit(message)
