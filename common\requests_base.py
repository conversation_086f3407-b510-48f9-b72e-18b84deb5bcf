import requests
import random


class BaseRequest():

    def __init__(self, host, use_proxy=False):
        self.userAgentList = [
            "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; AcooBrowser; .NET CLR 1.1.4322; .NET CLR 2.0.50727)",
            "Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; Acoo Browser; SLCC1; .NET CLR 2.0.50727; Media Center PC 5.0; .NET CLR 3.0.04506)",
            "Mozilla/4.0 (compatible; MSIE 7.0; AOL 9.5; AOLBuild 4337.35; Windows NT 5.1; .NET CLR 1.1.4322; .NET CLR 2.0.50727)",
            "Mozilla/5.0 (Windows; U; MSIE 9.0; Windows NT 9.0; en-US)",
            "Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Win64; x64; Trident/5.0; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET CLR 2.0.50727; Media Center PC 6.0)",
            "Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 6.0; Trident/4.0; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET CLR 1.0.3705; .NET CLR 1.1.4322)",
            "Mozilla/4.0 (compatible; MSIE 7.0b; Windows NT 5.2; .NET CLR 1.1.4322; .NET CLR 2.0.50727; InfoPath.2; .NET CLR 3.0.04506.30)",
            "Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN) AppleWebKit/523.15 (KHTML, like Gecko, Safari/419.3) Arora/0.3 (Change: 287 c9dfb30)",
            "Mozilla/5.0 (X11; U; Linux; en-US) AppleWebKit/527+ (KHTML, like Gecko, Safari/419.3) Arora/0.6",
            "Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.8.1.2pre) Gecko/20070215 K-Ninja/2.1.1",
            "Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.9) Gecko/20080705 Firefox/3.0 Kapiko/3.0",
            "Mozilla/5.0 (X11; Linux i686; U;) Gecko/20070322 Kazehakase/0.4.5",
            "Mozilla/5.0 (X11; U; Linux i686; en-US; rv:1.9.0.8) Gecko Fedora/1.9.0.8-1.fc10 Kazehakase/0.5.6",
            "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/535.11 (KHTML, like Gecko) Chrome/17.0.963.56 Safari/535.11",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_3) AppleWebKit/535.20 (KHTML, like Gecko) Chrome/19.0.1036.7 Safari/535.20",
            "Opera/9.80 (Macintosh; Intel Mac OS X 10.6.8; U; fr) Presto/2.9.168 Version/11.52",
            "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.106 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36",
        ]
        self.session = requests.session()
        adapter = requests.adapters.HTTPAdapter(max_retries=20)  # 增加重试次数，保证低配置的电脑可以请求成功
        self.session.mount('https://', adapter)
        self.session.mount('http://', adapter)
        self.session.headers["User-Agent"] = random.choice(self.userAgentList)
        self.cookie = {}
        self.host = host
        self.headers = {}
        self.timeout = 240
        self.useProxy = use_proxy  # 是否使用代理服务器
        self.proxy = {
            'http': 'http://python:e0eph&<EMAIL>:8289',
            'https': 'http://python:e0eph&<EMAIL>:8289'
        }

        if self.host[len(self.host) - 1] == "/":
            self.host = self.host[0:len(self.host) - 1]

    def get(self, url, headers=None, **kwargs):

        # TODO:如果传入的url以带有http://或者https:// 还需将self.host更新一下
        if self.host not in url and not str(url).startswith("http://") and not str(url).startswith("https://"):
            url = self.host + url

        if "Host" in self.headers:
            self.headers["Host"] = self.headers["Host"].replace("http://", "").replace("https://", "")
        _headers = {}
        if self.headers != None:
            _headers = self.headers
        if headers != None:
            _headers = headers
        # print(self.headers)  # headers=self.headers

        res = self.session.get(
            url, cookies=self.cookie, headers=_headers, timeout=self.timeout, proxies=self.proxy, **kwargs
        ) \
            if self.useProxy else self.session.get(
            url, cookies=self.cookie, headers=_headers, timeout=self.timeout, **kwargs
        )
        self.addCookie()
        return res

    def post(self, url, data=None, json=None, **kwargs):

        # TODO:如果传入的url以带有http://或者https:// 还需将self.host更新一下
        if self.host not in url and not str(url).startswith("http://") and not str(url).startswith("https://"):
            url = self.host + url
        # self.headers["Referer"] = url

        # print(self.headers)

        res = self.session.post(
            url, data=data, json=json, cookies=self.cookie, headers=self.headers, timeout=self.timeout,
            proxies=self.proxy, **kwargs
        ) \
            if self.useProxy else self.session.post(
            url, data=data, json=json, cookies=self.cookie, headers=self.headers, timeout=self.timeout, **kwargs
        )
        self.addCookie()
        return res

    def delete(self, url, **kwargs):

        # TODO:如果传入的url以带有http://或者https:// 还需将self.host更新一下
        if self.host not in url and not str(url).startswith("http://") and not str(url).startswith("https://"):
            url = self.host + url

        # self.headers["Referer"] = url
        res = self.session.delete(
            url=url, cookies=self.cookie, headers=self.headers, timeout=self.timeout, proxies=self.proxy, **kwargs
        ) \
            if self.useProxy else self.session.delete(
            url=url, cookies=self.cookie, headers=self.headers, timeout=self.timeout, **kwargs
        )
        self.addCookie()
        return res

    def put(self, url, **kwargs):

        # TODO:如果传入的url以带有http://或者https:// 还需将self.host更新一下
        if self.host not in url and not str(url).startswith("http://") and not str(url).startswith("https://"):
            url = self.host + url

        # self.headers["Referer"] = url
        res = self.session.put(
            url=url, cookies=self.cookie, headers=self.headers, timeout=self.timeout, proxies=self.proxy, **kwargs
        ) \
            if self.useProxy else self.session.put(
            url=url, cookies=self.cookie, headers=self.headers, timeout=self.timeout, **kwargs
        )
        self.addCookie()
        return res

    def addCookie(self):
        """
        添加Cookie,每次post完和请求完都添加一次Cookie,确保Cookie的完整性
        :return:
        """
        for c in self.session.cookies:
            self.cookie[c.name] = c.value
