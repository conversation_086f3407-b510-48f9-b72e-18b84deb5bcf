import sys
import os
import pathlib
import time
import subprocess
import PyQt5
import pythoncom
from UI.implement.AutoConnectVpnOptionDialog import AutoConnectVpnOptionDialog
from UI.implement.VersionInfoAutoConnectVpnDialog import VersionInfoAutoConnectVpnDialog
from UI.thread.AutoConnectVpnNewThread import AutoConnectVpnNewThread
from common.Utils.AutoConnectVpnUtils import AutoConnectVpnUtils
from common.Utils.FieldUtils import FileUtils
from PyQt5.QtCore import QVersionNumber, QT_VERSION_STR, Qt, QTranslator
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtWidgets import QApplication, QMainWindow, QMessageBox
from UI.autoConnectVpnMainForm import Ui_autoConnectVpnMainForm
from common.Utils.StringUtils import StringUtils

dirname = os.path.dirname(PyQt5.__file__)
qt_dir = os.path.join(dirname, 'Qt5', 'plugins', 'platforms')
os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = qt_dir


class Main(QMainWindow, Ui_autoConnectVpnMainForm):

    def __init__(self):
        """
        构造函数
        """
        super(Main, self).__init__()
        self.setupUi(self)
        pythoncom.CoInitialize()  # 初始化COM组件(解决无法使用剪切板的问题)

        # 初始化
        self.setWindowTitle("数字财政VPN重连软件 V2.023.1122.1")
        self.setWindowIcon(QIcon('static/icon/autoConnectVpn.ico'))
        self.setWindowFlag(Qt.MSWindowsFixedSizeDialogHint)  # 固定窗体大小（相当于禁用了最大化按钮）
        self.setFixedSize(549, 329)  # 固定主界面尺寸

        # 如果不存在report目录则创建
        path = pathlib.Path("report_auto_vpn")
        if not path.exists():
            path.mkdir(mode=0o777, parents=True, exist_ok=True)
        # 如果不存在config目录则创建
        path = pathlib.Path("config")
        if not path.exists():
            path.mkdir(mode=0o777, parents=True, exist_ok=True)
        # 如果不存在screen目录则创建
        path = pathlib.Path("screen_auto_vpn")
        if not path.exists():
            path.mkdir(mode=0o777, parents=True, exist_ok=True)

        # 初始化变量
        self.config = AutoConnectVpnUtils.readConfig()
        self.versionInfoAutoConnectVpnDialog = None
        self.autoConnectVpnOptionDialog = None

        # 定义事件
        self.action_option.triggered.connect(self.option)  # 选项设置
        self.action_version_info.triggered.connect(self.versionInfo)  # 版本说明
        self.action_manual.triggered.connect(self.openManual)  # 操作手册
        self.action_report_log.triggered.connect(self.openReportFolder)  # 报告日志
        self.action_exit.triggered.connect(self.exitMain)  # 退出

        # 初始化值
        self.textBrowser_auto_connect_vpn.clear()

        # 启动自动重连VPN线程
        self.autoConnectVpnThread = None
        self.startServiceTime = time.strftime("%Y%m%d %H%M%S", time.localtime())
        self.autoConnectVpnThread = AutoConnectVpnNewThread(
            self.config["vpnPath"], self.config["vpnTestUrl"], self.config["vpnIsDebug"]
        )
        self.autoConnectVpnThread.signalUpdateProgress.connect(self.slotReportProgress)  # 绑定线程信号方法（更新进度）
        self.autoConnectVpnThread.start()
        self.slotReportProgress("启动自动重连VPN线程")

    def slotReportProgress(self, message):
        """
        槽函数
        界面更新目前进度
        :param message:进度信息说明
        :return:
        """
        if len(message) > 0:
            self.textBrowser_auto_connect_vpn.append("{} {}".format(
                time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), message)
            )
            if self.isBrowserBottom(self.textBrowser_auto_connect_vpn):
                self.textBrowser_auto_connect_vpn.moveCursor(self.textBrowser_auto_connect_vpn.textCursor().End)
            self.saveReport()

    def exitMain(self):
        """
        关闭主窗体
        :return:
        """
        self.close()

    def closeEvent(self, event):
        """
        重写closeEvent方法，实现dialog窗体关闭时执行一些代码
        :param event: close()触发的事件
        :return: None
        """
        self.autoConnectVpnThread.stop = True
        self.autoConnectVpnThread.quit()

    def saveReport(self):
        """
        保存同步报告
        :return:
        """
        try:
            report_content = self.textBrowser_auto_connect_vpn.toPlainText()
            if StringUtils.isNotEmpty(report_content):
                report_path = "report_auto_vpn\\{} 自动VPN重连报告.txt".format(self.startServiceTime)
                FileUtils.saveFile(report_path, report_content, "w")
        except:
            pass

    def versionInfo(self):
        """
        版本说明
        :return:
        """
        if self.versionInfoAutoConnectVpnDialog is None:
            self.versionInfoAutoConnectVpnDialog = VersionInfoAutoConnectVpnDialog()
        self.versionInfoAutoConnectVpnDialog.exec()

    def openManual(self):
        """
        操作手册
        :return:
        """
        file_path = "数字财政VPN重连软件操作手册v1.0.pdf"
        path = pathlib.Path(file_path)
        if path.exists():
            subprocess.call("START {}".format(file_path), shell=True, stdin=subprocess.PIPE, stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
        else:
            self.slotReportProgress("操作手册 {} 丢失 请联系技术人员".format(file_path))

    def openReportFolder(self):
        """
        打开报告日志文件夹
        :return:
        """
        # 利用explorer.exe执行
        path = pathlib.Path("report_auto_vpn")
        if path.exists():
            subprocess.call("explorer.exe %s" % path.absolute(),
                            shell=True, stdin=subprocess.PIPE,
                            stdout=subprocess.PIPE, stderr=subprocess.PIPE)

    def option(self):
        """
        选项设置
        :return:
        """
        if self.autoConnectVpnOptionDialog is None:
            self.autoConnectVpnOptionDialog = AutoConnectVpnOptionDialog()
        self.autoConnectVpnOptionDialog.exec()
        self.config = AutoConnectVpnUtils.readConfig()  # 更新配置
        if self.autoConnectVpnThread is not None:
            self.autoConnectVpnThread.update(
                self.config["vpnPath"], self.config["vpnTestUrl"], self.config["vpnIsDebug"]
            )

    def isBrowserBottom(self, widget):
        """
        判断滑块是否在底部
        :param widget: qt控件
        """
        # 判断滑块是否在底部
        vertical_scroll_bar = widget.verticalScrollBar()
        if vertical_scroll_bar.value() == vertical_scroll_bar.maximum():
            return True
        else:
            return False


if __name__ == '__main__':
    try:
        v_compare = QVersionNumber(5, 6, 0)
        v_current, _ = QVersionNumber.fromString(QT_VERSION_STR)  # 获取当前Qt版本
        if QVersionNumber.compare(v_current, v_compare) >= 0:
            QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)  # Qt从5.6.0开始，支持High-DPI
            app = QApplication(sys.argv)  #
        else:
            app = QApplication(sys.argv)
            font = QFont("宋体")
            point_size = font.pointSize()
            font.setPixelSize(point_size * 90 / 72)
            app.setFont(font)
        # 下面这三行就是汉化的
        translator = QTranslator()
        translator.load('./static/qm/widgets_zh_cn.qm')
        app.installTranslator(translator)
        translator2 = QTranslator()
        translator2.load('./static/qm/qt_zh_CN.qm')
        app.installTranslator(translator2)
        # 打开主窗体
        my_main_win = Main()
        my_main_win.show()
        sys.exit(app.exec())
    except Exception as e:
        QMessageBox.information(None, '提示', "{}\r\n请联系技术人员".format(str(e).strip()))
        sys.exit()
