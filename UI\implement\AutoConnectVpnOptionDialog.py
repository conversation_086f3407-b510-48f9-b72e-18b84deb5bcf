import os

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon
from PyQt5.QtWidgets import QDialog, QMessageBox, QPushButton

from UI.autoConnectVpnOptionDialog import Ui_autoConnectVpnOptionDialog
from common.Utils.AutoConnectVpnUtils import AutoConnectVpnUtils
from common.Utils.StringUtils import StringUtils


class AutoConnectVpnOptionDialog(QDialog, Ui_autoConnectVpnOptionDialog):  # 继承QDialog可以有模态窗体的效果
    def __init__(self):
        """
        选项配置
        """
        super(AutoConnectVpnOptionDialog, self).__init__()
        self.setupUi(self)
        self.setWindowTitle("选项配置")
        self.setWindowIcon(QIcon('static/icon/autoConnectVpn.ico'))
        self.setWindowFlag(Qt.MSWindowsFixedSizeDialogHint)  # 固定窗体大小

        # 初始化变量
        self.config = AutoConnectVpnUtils.readConfig()
        self.lineEdit_vpn_path.setText(self.config["vpnPath"])  # 数字财政VPN工具路径
        self.lineEdit_vpn_test_url.setText(self.config["vpnTestUrl"])   # 数字财政测试地址
        self.checkBox_vpn_debug.setChecked(self.config["vpnIsDebug"])

        # 定义事件
        self.pushButton_save.clicked.connect(self.save)  # 保存
        self.pushButton_cancel.clicked.connect(self.cancel)  # 取消
        self.pushButton_default_vpn_path.clicked.connect(self.defaultVpnPath)  # 默认VPN工具路径
        self.pushButton_default_test_url.clicked.connect(self.defaultVpnTestUrl)    # 默认数字财政测试地址
        self.checkBox_vpn_debug.stateChanged.connect(self.checkDebug)  # 进入调试模式

    def save(self):
        """
        保存选项配置
        """
        try:
            if StringUtils.isEmpty(self.lineEdit_vpn_path.text().strip()):
                self.showTips("必须填入数字财政VPN工具路径")
                return
            vpn_path = self.lineEdit_vpn_path.text().strip()
            vpn_test_url = self.lineEdit_vpn_test_url.text().strip()
            if not os.path.exists(vpn_path):
                self.showTips("数字财政VPN工具路径文件不存在 请确认是否选择正确")
                return
            if StringUtils.isEmpty(self.lineEdit_vpn_test_url.text().strip()):
                self.showTips("必须填入数字财政测试连接地址")
                return
            # 保存配置
            AutoConnectVpnUtils.saveConfig("vpnPath", vpn_path)
            AutoConnectVpnUtils.saveConfig("vpnTestUrl", vpn_test_url)
            self.config["vpnPath"] = vpn_path
            self.config["vpnTestUrl"] = vpn_test_url
            self.checkDebug()  # 保存调试模式
            self.showTips("成功保存选项配置")
        except Exception as e:
            self.showTips("保存选项配置失败 原因: {}".format(str(e).strip()))

    def cancel(self):
        """
        取消
        """
        self.reject()

    def defaultVpnPath(self):
        """
        设置默认VPN工具路径
        """
        if os.path.exists(r"C:\Program Files (x86)"):
            default_vpn_path = r"C:\Program Files (x86)\UniVPN\UniVPN.exe"
        else:
            default_vpn_path = r"C:\Program Files\UniVPN\UniVPN.exe"
        if StringUtils.isEmpty(self.lineEdit_vpn_path.text().strip()):
            self.lineEdit_vpn_path.setText(default_vpn_path)
        else:
            message_box = QMessageBox()
            message_box.setWindowTitle("操作提问")
            message_box.setWindowIcon(QIcon('static/icon/autoConnectVpn.ico'))
            message_box.setText("系统检测出已设置了数字财政VPN工具路径，是否覆盖为默认数字财政VPN工具路径？")
            message_box.addButton(QPushButton('好的'), QMessageBox.AcceptRole)
            message_box.addButton(QPushButton('忽略'), QMessageBox.RejectRole)
            if message_box.exec() == QMessageBox.AcceptRole:
                self.lineEdit_vpn_path.setText(default_vpn_path)

    def defaultVpnTestUrl(self):
        """
        设置默认数字财政测试连接地址
        """
        default_vpn_test_url = "http://ctglqscwg.gdczt.gov.cn/zxy_guangzhou/"
        if StringUtils.isEmpty(self.lineEdit_vpn_test_url.text().strip()):
            self.lineEdit_vpn_test_url.setText(default_vpn_test_url)
        else:
            message_box = QMessageBox()
            message_box.setWindowTitle("操作提问")
            message_box.setWindowIcon(QIcon('static/icon/autoConnectVpn.ico'))
            message_box.setText("系统检测出已设置了数字财政测试连接地址，是否覆盖为默认数字财政测试连接地址？")
            message_box.addButton(QPushButton('好的'), QMessageBox.AcceptRole)
            message_box.addButton(QPushButton('忽略'), QMessageBox.RejectRole)
            if message_box.exec() == QMessageBox.AcceptRole:
                self.lineEdit_vpn_test_url.setText(default_vpn_test_url)

    def checkDebug(self):
        """
        是否进入调试模式
        :return:
        """
        if self.checkBox_vpn_debug.isChecked():
            AutoConnectVpnUtils.saveConfig("vpnIsDebug", True)
            self.config["vpnIsDebug"] = True
            self.showTips("进入调试模式")
        else:
            AutoConnectVpnUtils.saveConfig("vpnIsDebug", False)
            self.config["vpnIsDebug"] = False
            self.showTips("取消调试模式")

    def showTips(self, message):
        """
        显示提示信息
        :param message: 提示信息
        :return:
        """
        self.label_tips.setText('<font style="color:blue">{}</font>'.format(message))
