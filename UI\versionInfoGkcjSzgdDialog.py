# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'versionInfoGkcjSzgdDialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_versionInfoGkcjSzgdDialog(object):
    def setupUi(self, versionInfoGkcjSzgdDialog):
        versionInfoGkcjSzgdDialog.setObjectName("versionInfoGkcjSzgdDialog")
        versionInfoGkcjSzgdDialog.resize(400, 300)
        self.textBrowser_version_info = QtWidgets.QTextBrowser(versionInfoGkcjSzgdDialog)
        self.textBrowser_version_info.setGeometry(QtCore.QRect(10, 10, 381, 281))
        self.textBrowser_version_info.setObjectName("textBrowser_version_info")

        self.retranslateUi(versionInfoGkcjSzgdDialog)
        QtCore.QMetaObject.connectSlotsByName(versionInfoGkcjSzgdDialog)

    def retranslateUi(self, versionInfoGkcjSzgdDialog):
        _translate = QtCore.QCoreApplication.translate
        versionInfoGkcjSzgdDialog.setWindowTitle(_translate("versionInfoGkcjSzgdDialog", "版本说明"))
