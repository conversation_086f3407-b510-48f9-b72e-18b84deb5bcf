import time

from common.requests_base import BaseRequest
from common.Utils.StringUtils import StringUtils


class TimeUtils:
    """
    时间日期处理类
    """

    @staticmethod
    def getBeijinTime():
        """
    　　 获取北京时间(需要网络)
        """
        try:
            host = "https://www.beijing-time.org"
            request = BaseRequest(host)
            res = request.get("/t/time.asp")
            if res.status_code == 200:
                # 解析响应的消息
                data = res.text.split("\r\n")
                year = data[1][len("nyear") + 1: len(data[1]) - 1]
                month = data[2][len("nmonth") + 1: len(data[2]) - 1]
                day = data[3][len("nday") + 1: len(data[3]) - 1]
                # wday = data[4][len("nwday")+1 : len(data[4])-1]
                hrs = data[5][len("nhrs") + 1: len(data[5]) - 1]
                minute = data[6][len("nmin") + 1: len(data[6]) - 1]
                sec = data[7][len("nsec") + 1: len(data[7]) - 1]
                time_str = "%s-%s-%s %s:%s:%s" % (year, month, day, hrs, minute, sec)
                print("获取网络时间: {}".format(time_str))
                return time.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        except:
            return None

    @staticmethod
    def getCurrentTime():
        """
        获取当前时间 先从北京时间获取 否则从本地时间获取
        :return:
        """
        # 由于网络问题取消获取北京时间
        # current_time = TimeUtils.getBeijinTime()
        current_time = None
        if current_time is None:
            time_str = StringUtils.getLocalDate("%Y-%m-%d %H:%M:%S")
            print("获取本地时间: {}".format(time_str))
            current_time = time.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        return current_time

    @staticmethod
    def getLocalTime():
        """
        获取本地时间
        :return:
        """
        time_str = StringUtils.getLocalDate("%Y-%m-%d %H:%M:%S")
        print("获取本地时间: {}".format(time_str))
        return time.strptime(time_str, "%Y-%m-%d %H:%M:%S")
