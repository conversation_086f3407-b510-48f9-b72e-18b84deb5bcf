import json
import os

from common.Utils.FieldUtils import FileUtils


class AutoConnectVpnUtils:
    """
    数字财政VPN自动重连处理常用类
    """

    @staticmethod
    def readConfig():
        """
        读取配置信息
        :return:
        """
        try:
            config_path = r"config\autoConnectVpnConfig.json"
            content = FileUtils.read(config_path)
            dict_config = json.loads(content)
            #  判断是否缺少配置键值，有则补充默认值
            is_lack_key = False
            if "vpnPath" not in dict_config:
                if os.path.exists(r"C:\Program Files (x86)"):
                    dict_config["vpnPath"] = r"C:\Program Files (x86)\UniVPN\UniVPN.exe"
                else:
                    dict_config["vpnPath"] = r"C:\Program Files (x86)\UniVPN\UniVPN.exe"
                is_lack_key = True
            if "vpnTestUrl" not in dict_config:
                dict_config["vpnTestUrl"] = "http://ctglqscwg.gdczt.gov.cn/zxy_guangzhou/"
                is_lack_key = True
            if "vpnIsDebug" not in dict_config:
                dict_config["vpnIsDebug"] = False
                is_lack_key = True
            if is_lack_key:
                try:
                    FileUtils.saveFile(config_path, json.dumps(dict_config), "w")
                except:
                    pass
            return dict_config
        except:
            if os.path.exists(r"C:\Program Files (x86)"):
                vpn_path = r"C:\Program Files (x86)\UniVPN\UniVPN.exe"
            else:
                vpn_path = r"C:\Program Files\UniVPN\UniVPN.exe"
            dict_config = {
                "vpnPath": vpn_path,
                "vpnTestUrl": "http://ctglqscwg.gdczt.gov.cn/zxy_guangzhou/",
                "vpnIsDebug": False
            }
            try:
                FileUtils.saveFile(config_path, json.dumps(dict_config), "w")
            except:
                pass
            return dict_config

    @staticmethod
    def saveConfig(key, value):
        """
        保存配置信息
        :param key: 配置键值
        :param value: 配置值
        :return: True-成功保存 False-保存失败
        """
        dict_config = AutoConnectVpnUtils.readConfig()
        try:
            config_path = r"config\autoConnectVpnConfig.json"
            dict_config[key] = value
            FileUtils.saveFile(config_path, json.dumps(dict_config), "w")
            return True
        except:
            return False
