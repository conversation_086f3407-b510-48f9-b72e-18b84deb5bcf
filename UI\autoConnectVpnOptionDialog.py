# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'autoConnectVpnOptionDialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_autoConnectVpnOptionDialog(object):
    def setupUi(self, autoConnectVpnOptionDialog):
        autoConnectVpnOptionDialog.setObjectName("autoConnectVpnOptionDialog")
        autoConnectVpnOptionDialog.resize(516, 180)
        self.pushButton_cancel = QtWidgets.QPushButton(autoConnectVpnOptionDialog)
        self.pushButton_cancel.setGeometry(QtCore.QRect(430, 152, 70, 23))
        self.pushButton_cancel.setObjectName("pushButton_cancel")
        self.label_tips = QtWidgets.QLabel(autoConnectVpnOptionDialog)
        self.label_tips.setGeometry(QtCore.QRect(10, 130, 411, 41))
        self.label_tips.setAutoFillBackground(False)
        self.label_tips.setTextFormat(QtCore.Qt.RichText)
        self.label_tips.setScaledContents(True)
        self.label_tips.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignTop)
        self.label_tips.setWordWrap(True)
        self.label_tips.setObjectName("label_tips")
        self.pushButton_save = QtWidgets.QPushButton(autoConnectVpnOptionDialog)
        self.pushButton_save.setGeometry(QtCore.QRect(430, 124, 70, 23))
        self.pushButton_save.setObjectName("pushButton_save")
        self.groupBox_vpn = QtWidgets.QGroupBox(autoConnectVpnOptionDialog)
        self.groupBox_vpn.setGeometry(QtCore.QRect(10, 10, 491, 111))
        self.groupBox_vpn.setObjectName("groupBox_vpn")
        self.lineEdit_vpn_path = QtWidgets.QLineEdit(self.groupBox_vpn)
        self.lineEdit_vpn_path.setGeometry(QtCore.QRect(130, 20, 281, 20))
        self.lineEdit_vpn_path.setObjectName("lineEdit_vpn_path")
        self.label_vpn_path = QtWidgets.QLabel(self.groupBox_vpn)
        self.label_vpn_path.setGeometry(QtCore.QRect(10, 20, 121, 21))
        self.label_vpn_path.setObjectName("label_vpn_path")
        self.pushButton_default_vpn_path = QtWidgets.QPushButton(self.groupBox_vpn)
        self.pushButton_default_vpn_path.setGeometry(QtCore.QRect(413, 19, 70, 23))
        self.pushButton_default_vpn_path.setObjectName("pushButton_default_vpn_path")
        self.checkBox_vpn_debug = QtWidgets.QCheckBox(self.groupBox_vpn)
        self.checkBox_vpn_debug.setGeometry(QtCore.QRect(330, 80, 141, 16))
        self.checkBox_vpn_debug.setObjectName("checkBox_vpn_debug")
        self.lineEdit_vpn_test_url = QtWidgets.QLineEdit(self.groupBox_vpn)
        self.lineEdit_vpn_test_url.setGeometry(QtCore.QRect(130, 50, 281, 20))
        self.lineEdit_vpn_test_url.setObjectName("lineEdit_vpn_test_url")
        self.label_vpn_test_url = QtWidgets.QLabel(self.groupBox_vpn)
        self.label_vpn_test_url.setGeometry(QtCore.QRect(10, 50, 121, 21))
        self.label_vpn_test_url.setObjectName("label_vpn_test_url")
        self.pushButton_default_test_url = QtWidgets.QPushButton(self.groupBox_vpn)
        self.pushButton_default_test_url.setGeometry(QtCore.QRect(413, 50, 70, 23))
        self.pushButton_default_test_url.setObjectName("pushButton_default_test_url")

        self.retranslateUi(autoConnectVpnOptionDialog)
        QtCore.QMetaObject.connectSlotsByName(autoConnectVpnOptionDialog)

    def retranslateUi(self, autoConnectVpnOptionDialog):
        _translate = QtCore.QCoreApplication.translate
        autoConnectVpnOptionDialog.setWindowTitle(_translate("autoConnectVpnOptionDialog", "选项配置"))
        self.pushButton_cancel.setText(_translate("autoConnectVpnOptionDialog", "取消"))
        self.label_tips.setText(_translate("autoConnectVpnOptionDialog", "<font style=\"color:blue;\">提示信息：</font>"))
        self.pushButton_save.setText(_translate("autoConnectVpnOptionDialog", "保存"))
        self.groupBox_vpn.setTitle(_translate("autoConnectVpnOptionDialog", "VPN自动重连配置"))
        self.lineEdit_vpn_path.setPlaceholderText(_translate("autoConnectVpnOptionDialog", "如：C:\\Program Files (x86)\\SecoClient\\SecoClient.exe"))
        self.label_vpn_path.setText(_translate("autoConnectVpnOptionDialog", "数字财政VPN工具路径:"))
        self.pushButton_default_vpn_path.setText(_translate("autoConnectVpnOptionDialog", "默认路径"))
        self.checkBox_vpn_debug.setText(_translate("autoConnectVpnOptionDialog", "进入VPN重连调试模式"))
        self.lineEdit_vpn_test_url.setPlaceholderText(_translate("autoConnectVpnOptionDialog", "如：http://ctglqscwg.gdczt.gov.cn/zxy_guangzhou/"))
        self.label_vpn_test_url.setText(_translate("autoConnectVpnOptionDialog", "数字财政检测连接地址:"))
        self.pushButton_default_test_url.setText(_translate("autoConnectVpnOptionDialog", "默认地址"))
