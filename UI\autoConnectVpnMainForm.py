# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'autoConnectVpnMainForm.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_autoConnectVpnMainForm(object):
    def setupUi(self, autoConnectVpnMainForm):
        autoConnectVpnMainForm.setObjectName("autoConnectVpnMainForm")
        autoConnectVpnMainForm.resize(549, 329)
        self.centralwidget = QtWidgets.QWidget(autoConnectVpnMainForm)
        self.centralwidget.setObjectName("centralwidget")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.centralwidget)
        self.verticalLayout.setContentsMargins(1, 1, 1, 1)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.textBrowser_auto_connect_vpn = QtWidgets.QTextBrowser(self.centralwidget)
        self.textBrowser_auto_connect_vpn.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.textBrowser_auto_connect_vpn.setAcceptRichText(True)
        self.textBrowser_auto_connect_vpn.setObjectName("textBrowser_auto_connect_vpn")
        self.verticalLayout.addWidget(self.textBrowser_auto_connect_vpn)
        autoConnectVpnMainForm.setCentralWidget(self.centralwidget)
        self.toolBar_normal = QtWidgets.QToolBar(autoConnectVpnMainForm)
        self.toolBar_normal.setObjectName("toolBar_normal")
        autoConnectVpnMainForm.addToolBar(QtCore.Qt.TopToolBarArea, self.toolBar_normal)
        self.action_version_info = QtWidgets.QAction(autoConnectVpnMainForm)
        self.action_version_info.setObjectName("action_version_info")
        self.action_option = QtWidgets.QAction(autoConnectVpnMainForm)
        self.action_option.setObjectName("action_option")
        self.action_manual = QtWidgets.QAction(autoConnectVpnMainForm)
        self.action_manual.setObjectName("action_manual")
        self.action_report_log = QtWidgets.QAction(autoConnectVpnMainForm)
        self.action_report_log.setObjectName("action_report_log")
        self.action_exit = QtWidgets.QAction(autoConnectVpnMainForm)
        self.action_exit.setObjectName("action_exit")
        self.toolBar_normal.addAction(self.action_option)
        self.toolBar_normal.addAction(self.action_version_info)
        self.toolBar_normal.addAction(self.action_manual)
        self.toolBar_normal.addAction(self.action_report_log)
        self.toolBar_normal.addAction(self.action_exit)

        self.retranslateUi(autoConnectVpnMainForm)
        QtCore.QMetaObject.connectSlotsByName(autoConnectVpnMainForm)

    def retranslateUi(self, autoConnectVpnMainForm):
        _translate = QtCore.QCoreApplication.translate
        autoConnectVpnMainForm.setWindowTitle(_translate("autoConnectVpnMainForm", "数字财政VPN自动重连工具"))
        self.textBrowser_auto_connect_vpn.setPlaceholderText(_translate("autoConnectVpnMainForm", "VPN自动重连报告"))
        self.toolBar_normal.setWindowTitle(_translate("autoConnectVpnMainForm", "toolBar"))
        self.action_version_info.setText(_translate("autoConnectVpnMainForm", "版本说明"))
        self.action_option.setText(_translate("autoConnectVpnMainForm", "选项"))
        self.action_manual.setText(_translate("autoConnectVpnMainForm", "操作手册"))
        self.action_report_log.setText(_translate("autoConnectVpnMainForm", "重连日志"))
        self.action_exit.setText(_translate("autoConnectVpnMainForm", "退出"))
