from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon
from PyQt5.QtWidgets import QDialog

from UI.versionInfoGkcjSzgdDialog import Ui_versionInfoGkcjSzgdDialog


class VersionInfoAutoConnectVpnDialog(QDialog, Ui_versionInfoGkcjSzgdDialog):  # 继承QDialog可以有模态窗体的效果
    def __init__(self):
        """
        数字财政VPN自动重连版本说明
        """
        super(VersionInfoAutoConnectVpnDialog, self).__init__()
        self.setupUi(self)
        self.setWindowTitle("版本说明")
        self.setWindowIcon(QIcon('static/icon/autoConnectVpn.ico'))
        self.setWindowFlag(Qt.MSWindowsFixedSizeDialogHint)  # 固定窗体大小
        # 初始化版本信息
        self.textBrowser_version_info.setText('''当前最新版本：
V1.022.1129.1
1）初始发布独立功能版本；
2）将取消国库对接自动重连VPN功能，提高自动重连稳定性；
3）增加调试模式下自动截图功能，便于追踪无法自动重连的情况；
4）优化采用获取截图方式重连锁屏出错的问题；

V2.023.1122.1
1）升级适用于UniVPN软件
        ''')
