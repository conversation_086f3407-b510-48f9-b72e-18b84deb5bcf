import time
import uuid


class StringUtils():

    @staticmethod
    def isEmpty(str):
        """
        判断字符串是否为空
        :param str:
        :return:
        """
        if str is None or str.strip() == "":
            return True
        return False

    @staticmethod
    def isNotEmpty(str):
        """
        判断字符串是否不为空
        :param str:
        :return:
        """
        return not StringUtils.isEmpty(str)

    @staticmethod
    def getNewUUID():
        """
        获取一个没有横杠的uuid
        :return:
        """
        uid = str(uuid.uuid4())
        return uid.replace("-", "")

    @staticmethod
    def getLocalDate(format="%Y%m%d%H%M%S"):
        """
         获取时间，默认格式（%Y%m%d%H%M%S）
        :param format: 日期格式
        :return:
        """
        local_time = time.localtime(time.time())
        data_head = time.strftime(format, local_time)
        return data_head

    @staticmethod
    def getMidStr(s, charS='[', charE=']'):
        """
        获取中间的支付串：
        输入：[12312]哈哈
        输出：12312
        :param s:原始字符串
        :param charS:开始的字符
        :param charE:结束字符
        :return:
        """
        if StringUtils.isEmpty(s):
            return ""
        return s[s.find(charS) + 1:s.find(charE)]
