import requests
import psutil
import subprocess
import pathlib
from pywinauto.application import Application


def test_connect():
    try:
        url = "http://baiduo.com/"
        res = requests.get(url)  # "http://www.baidu.com"
        if res.status_code != 200:
            print("False", res.status_code, res.text)
        else:
            print("True 连接成功！", res.text)
    except requests.exceptions.ConnectionError as e:
        print("False 连接错误", str(e).strip())


def test_process():
    # 获取当前正在运行的进程列表
    process_list = psutil.process_iter()
    for process in process_list:
        if process.name() == "UniVPN.exe":
            print("Process is running.")
            # 结束进程
            process.terminate()
            print("Process is killed")
            break
    else:
        print("Process is not running.")


def test_open_process():
    # 启动进程
    program_path = r"C:\Program Files (x86)\UniVPN\UniVPN.exe"
    try:
        subprocess.Popen(program_path)
        print("Program started!")
    except FileNotFoundError:
        print("File not found.")
    except OSError:
        print("OS error occurred.")
    except Exception as err:
        print("启动进程失败, Unknown error occurred. {}".format(str(err).strip()))


def getStaticContent(str_ctrl):
    """
    获取静态文本内容
    """
    start_index = 0
    start = str_ctrl.index("Static - '", start_index)
    while start >= 0:
        end = str_ctrl.index("'", start + len("Static - '"))
        content = str_ctrl[start + len("Static - '"):end]
        if len(str(content).strip()) > 0:
            return content
        start = str_ctrl.index("Static - '", end)
    return ""


def test_enter_win():
    # 点击警告窗继续
    app = Application(backend="uia").connect(path=r"C:\Program Files (x86)\UniVPN\UniVPN.exe")
    pywinautoPath = pathlib.Path("./config/pywinauto").resolve()
    try:
        dlg_warn = app["警告"]
        dlg_warn.print_control_identifiers(filename=pywinautoPath)
        with open(pywinautoPath, 'rt', encoding="gbk") as f:
            f.seek(0)  # 移动文件指针
            str_ctrl = f.read()
        content = getStaticContent(str_ctrl)
        print("成功获取窗口内容: {}".format(content))
        dlg_warn['确定 Enter'].click()  # 发现警告窗口，就点击确定
        print("点击警告框确定 框信息: {}".format(content))
        return
    except Exception as err1:
        print("未找到警告确认框1, {}".format(str(err1).strip()))


def test():
    import pathlib
    import os
    # try:
    #     path = r'C:\Program Files (x86)\UniVPN\uninst.exe'
    #     app = Application(backend="uia").connect(path=path)
    #     pywinautoPath = pathlib.Path("./config/pywinauto").resolve()
    # except Exception as e:
    #     print(str(e))
    os.system(r'"C:\Program Files (x86)\UniVPN\UniVPN.exe"')


if __name__ == '__main__':
    # test_connect()
    test_process()
    # test_open_process()
    # test_enter_win()
    # test()
