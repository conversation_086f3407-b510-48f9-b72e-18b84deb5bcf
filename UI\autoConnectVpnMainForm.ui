<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>autoConnectVpnMainForm</class>
 <widget class="QMainWindow" name="autoConnectVpnMainForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>549</width>
    <height>329</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>数字财政VPN自动重连工具</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>1</number>
    </property>
    <property name="topMargin">
     <number>1</number>
    </property>
    <property name="rightMargin">
     <number>1</number>
    </property>
    <property name="bottomMargin">
     <number>1</number>
    </property>
    <item>
     <widget class="QTextBrowser" name="textBrowser_auto_connect_vpn">
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="acceptRichText">
       <bool>true</bool>
      </property>
      <property name="placeholderText">
       <string>VPN自动重连报告</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QToolBar" name="toolBar_normal">
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="action_option"/>
   <addaction name="action_version_info"/>
   <addaction name="action_manual"/>
   <addaction name="action_report_log"/>
   <addaction name="action_exit"/>
  </widget>
  <action name="action_version_info">
   <property name="text">
    <string>版本说明</string>
   </property>
  </action>
  <action name="action_option">
   <property name="text">
    <string>选项</string>
   </property>
  </action>
  <action name="action_manual">
   <property name="text">
    <string>操作手册</string>
   </property>
  </action>
  <action name="action_report_log">
   <property name="text">
    <string>重连日志</string>
   </property>
  </action>
  <action name="action_exit">
   <property name="text">
    <string>退出</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
