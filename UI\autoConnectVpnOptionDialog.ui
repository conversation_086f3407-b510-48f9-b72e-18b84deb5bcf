<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>autoConnectVpnOptionDialog</class>
 <widget class="QDialog" name="autoConnectVpnOptionDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>516</width>
    <height>180</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>选项配置</string>
  </property>
  <widget class="QPushButton" name="pushButton_cancel">
   <property name="geometry">
    <rect>
     <x>430</x>
     <y>152</y>
     <width>70</width>
     <height>23</height>
    </rect>
   </property>
   <property name="text">
    <string>取消</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_tips">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>130</y>
     <width>411</width>
     <height>41</height>
    </rect>
   </property>
   <property name="autoFillBackground">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>&lt;font style=&quot;color:blue;&quot;&gt;提示信息：&lt;/font&gt;</string>
   </property>
   <property name="textFormat">
    <enum>Qt::RichText</enum>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
   <property name="alignment">
    <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
   </property>
   <property name="wordWrap">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_save">
   <property name="geometry">
    <rect>
     <x>430</x>
     <y>124</y>
     <width>70</width>
     <height>23</height>
    </rect>
   </property>
   <property name="text">
    <string>保存</string>
   </property>
  </widget>
  <widget class="QGroupBox" name="groupBox_vpn">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>10</y>
     <width>491</width>
     <height>111</height>
    </rect>
   </property>
   <property name="title">
    <string>VPN自动重连配置</string>
   </property>
   <widget class="QLineEdit" name="lineEdit_vpn_path">
    <property name="geometry">
     <rect>
      <x>130</x>
      <y>20</y>
      <width>281</width>
      <height>20</height>
     </rect>
    </property>
    <property name="placeholderText">
     <string>如：C:\Program Files (x86)\SecoClient\SecoClient.exe</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_vpn_path">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>20</y>
      <width>121</width>
      <height>21</height>
     </rect>
    </property>
    <property name="text">
     <string>数字财政VPN工具路径:</string>
    </property>
   </widget>
   <widget class="QPushButton" name="pushButton_default_vpn_path">
    <property name="geometry">
     <rect>
      <x>413</x>
      <y>19</y>
      <width>70</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>默认路径</string>
    </property>
   </widget>
   <widget class="QCheckBox" name="checkBox_vpn_debug">
    <property name="geometry">
     <rect>
      <x>330</x>
      <y>80</y>
      <width>141</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>进入VPN重连调试模式</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="lineEdit_vpn_test_url">
    <property name="geometry">
     <rect>
      <x>130</x>
      <y>50</y>
      <width>281</width>
      <height>20</height>
     </rect>
    </property>
    <property name="placeholderText">
     <string>如：http://ctglqscwg.gdczt.gov.cn/zxy_guangzhou/</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_vpn_test_url">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>50</y>
      <width>121</width>
      <height>21</height>
     </rect>
    </property>
    <property name="text">
     <string>数字财政检测连接地址:</string>
    </property>
   </widget>
   <widget class="QPushButton" name="pushButton_default_test_url">
    <property name="geometry">
     <rect>
      <x>413</x>
      <y>50</y>
      <width>70</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>默认地址</string>
    </property>
   </widget>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
