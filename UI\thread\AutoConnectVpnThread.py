import pathlib

import pyautogui
from PyQt5.QtCore import *
from pywinauto.application import Application

from common.Utils.FieldUtils import FileUtils
from common.Utils.StringUtils import StringUtils


class AutoConnectVpnThread(QThread):
    """
    报告状态线程类
    """
    # 更新进度信号
    signalUpdateProgress = pyqtSignal(str)  # str:进度信息

    def __init__(self, path, get_type, is_debug):
        # 设置线程初始工作状态
        super(AutoConnectVpnThread, self).__init__()
        self.stop = False
        self.path = path
        self.getType = get_type
        self.isDebug = True if is_debug == "True" else False
        self.app = None
        self.pywinautoPath = pathlib.Path("./config/pywinauto").resolve()
        self.pngAutoVpnList = [
            {
                "title": "强制下线警告框",
                "dialog_png_path": "./auto_vpn/png/01强制下线.png",
                "button_png_path": "./auto_vpn/png/01强制下线_确定.png",
                "button_name": "确定",
            },
            {
                "title": "网卡异常警告框",
                "dialog_png_path": "./auto_vpn/png/02网卡异常.png",
                "button_png_path": "./auto_vpn/png/02网卡异常_确定.png",
                "button_name": "确定",
            },
            {
                "title": "SecoClient连接框",
                "dialog_png_path": "./auto_vpn/png/03SecoClient标题.png",
                "button_png_path": "./auto_vpn/png/03SecoClient标题_连接.png",
                "button_name": "连接",
            },
            {
                "title": "登录确认框",
                "dialog_png_path": "./auto_vpn/png/04登录确认.png",
                "button_png_path": "./auto_vpn/png/04登录确认_登录.png",
                "button_name": "登录",
            },
            {
                "title": "安全警告框",
                "dialog_png_path": "./auto_vpn/png/05安全警告.png",
                "button_png_path": "./auto_vpn/png/05安全警告_继续.png",
                "button_name": "继续",
            },
        ]
        if not pathlib.Path(path).exists():
            self.stop = True  # 停止自动重连VPN
            self.signalUpdateProgress.emit("数字财政VPN工具路径有误 请确认是否选择正确 目前不会自动重连VPN")

    def __del__(self):
        # 线程状态改变与线程终止
        pass

    def run(self):
        """
        线程相关的执行代码
        """
        while not self.stop:
            try:
                self.emitSignalUpdateProgress("================================================")
                self.emitSignalUpdateProgress("{} 定时检测是否断开".format(self.getType))
                if self.getType == "采用获取控件方式重连":
                    self.app = Application(backend="uia").connect(path=self.path)
                    # 尝试点击警告框
                    try:
                        dlg_warn = self.app["警告"]
                        self.emitSignalUpdateProgress("成功获取警告窗口")
                        # dlg_warn.print_control_identifiers()
                        dlg_warn.print_control_identifiers(filename=self.pywinautoPath)
                        with open(self.pywinautoPath, 'rt', encoding="gbk") as f:
                            f.seek(0)  # 移动文件指针
                            str_ctrl = f.read()
                        content = self.getStaticContent(str_ctrl)
                        self.emitSignalUpdateProgress("成功获取窗口内容: {}".format(content))
                        dlg_warn['确定 Enter'].click()  # 发现警告窗口，就点击确定
                        self.signalUpdateProgress.emit("点击警告框确定 框信息: {}".format(content))
                    except Exception as e:
                        self.emitSignalUpdateProgress("未找到警告确定框".format(str(e).strip()))
                    self.sleep(3)
                    # 尝试点击警告框2
                    try:
                        dlg_warn2 = self.app["警告"]
                        self.emitSignalUpdateProgress("成功获取警告窗口")
                        # dlg_warn2.print_control_identifiers()
                        dlg_warn2.print_control_identifiers(filename=self.pywinautoPath)
                        with open(self.pywinautoPath, 'rt', encoding="gbk") as f:
                            f.seek(0)  # 移动文件指针
                            str_ctrl = f.read()
                        content = self.getStaticContent(str_ctrl)
                        self.emitSignalUpdateProgress("成功获取窗口内容: {}".format(content))
                        dlg_warn2['继续 Enter'].click()  # 发现警告窗口，就点击确定
                        self.signalUpdateProgress.emit("点击警告框确定 框信息: {}".format(content))
                    except Exception as e:
                        self.emitSignalUpdateProgress("未找到警告确定框2".format(str(e).strip()))
                    self.sleep(3)
                    # 尝试点击secoClient框
                    try:
                        dlg_link = self.app["SecoClient"]
                        self.emitSignalUpdateProgress("成功获取SecoClient窗口")
                        # dlg_link.print_control_identifiers()
                        dlg_link.child_window(title="连接 Enter", control_type="Button").click()  # 发现secoClient窗口，就点击连接
                        self.signalUpdateProgress.emit("点击secoClient框连接")
                    except Exception as e:
                        self.emitSignalUpdateProgress("未找到secoClient框".format(str(e).strip()))
                    self.sleep(3)
                    # 尝试点击secoClient框2
                    try:
                        dlg_link2 = self.app["SecoClient"]
                        self.emitSignalUpdateProgress("成功获取SecoClient窗口")
                        # dlg_link2.print_control_identifiers()
                        dlg_link2.child_window(title="连接", control_type="Button").click()  # 发现secoClient窗口，就点击连接
                        self.signalUpdateProgress.emit("点击secoClient框连接")
                    except Exception as e:
                        self.emitSignalUpdateProgress("未找到secoClient框2".format(str(e).strip()))
                    self.sleep(3)
                    # 尝试点击登录框
                    try:
                        win_log = self.app["SecoClient"]
                        self.emitSignalUpdateProgress("成功获取SecoClient窗口")
                        dlg_log = win_log.child_window(title="登录", control_type="Window")
                        # dlg_log.print_control_identifiers()
                        dlg_log.child_window(title="登录 Enter", control_type="Button").click()  # 发现secoClient登录窗口，就点击登录
                        self.signalUpdateProgress.emit("点击登录框登录")
                    except Exception as e:
                        self.emitSignalUpdateProgress("未找到secoClient框3".format(str(e).strip()))
                    self.sleep(3)
                elif self.getType == "采用获取截图方式重连":
                    # 遍历所有可能出现的窗体并进行操作
                    # # 获取提示信息窗口
                    for pngAutoVpn in self.pngAutoVpnList:
                        if not pathlib.Path(pngAutoVpn["dialog_png_path"]).exists():
                            self.signalUpdateProgress.emit("图片{}不存在 请确保图片路径是否正确".format(
                                pathlib.Path(pngAutoVpn["dialog_png_path"]))
                            )
                            continue
                        if not pathlib.Path(pngAutoVpn["button_png_path"]).exists():
                            self.signalUpdateProgress.emit("图片{}不存在 请确保图片路径是否正确".format(
                                pathlib.Path(pngAutoVpn["button_png_path"]))
                            )
                            continue
                        location = pyautogui.locateOnScreen(image=pngAutoVpn["dialog_png_path"])
                        if location is not None:
                            self.signalUpdateProgress.emit("成功获取{}窗口 坐标:{}".format(
                                pngAutoVpn["title"], location)
                            )
                            # 获取点击按钮
                            x, y = pyautogui.locateCenterOnScreen(image=pngAutoVpn["button_png_path"])
                            if x > 0 and y > 0:
                                self.signalUpdateProgress.emit("成功获取按钮 坐标: {} {}".format(x, y))
                                pyautogui.click(x=x, y=y, clicks=1, button='left')
                                self.signalUpdateProgress.emit("点击{}按钮".format(pngAutoVpn["button_name"]))
                            else:
                                self.signalUpdateProgress.emit("获取按钮坐标失败")
                        else:
                            self.emitSignalUpdateProgress("未找到{}".format(pngAutoVpn["title"]))
                        self.sleep(5)
            except Exception as eOut:
                self.signalUpdateProgress.emit(str(eOut).strip())

    def getStaticContent(self, str_ctrl):
        """
        获取静态文本内容
        """
        start_index = 0
        start = str_ctrl.index("Static - '", start_index)
        while start >= 0:
            end = str_ctrl.index("'", start + len("Static - '"))
            content = str_ctrl[start + len("Static - '"):end]
            if StringUtils.isNotEmpty(str(content).strip()):
                return content
            start = str_ctrl.index("Static - '", end)
        return ""

    def emitSignalUpdateProgress(self, message):
        """
        更新进度信息
        :param message: 进度信息
        :return:
        """
        if not self.isDebug:
            return
        if StringUtils.isEmpty(message):
            return
        self.signalUpdateProgress.emit(message)
