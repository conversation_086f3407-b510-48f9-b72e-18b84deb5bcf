import time
import requests


class SzczConnector:
    def __init__(self, test_url):
        """数字财政相关"""
        self.url = test_url  # "http://ctglqscwg.gdczt.gov.cn/zxy_guangzhou/"

    def is_vpn_connected(self, try_time=3):
        """
        检查数字财政是否正常连接
        """
        err_msg = ""
        for try_time in range(try_time):
            try:
                res = requests.get(self.url)
                if res.status_code != 200:
                    err_msg = "返回状态码有误, {}, {}".format(res.status_code, res.text)
                    raise Exception(err_msg)
                else:
                    return True, ""
            except requests.exceptions.ConnectionError as req_err:
                err_msg = "访问数字财政失败, {}".format(str(req_err).strip())
                print(err_msg)
                time.sleep(2)
            except Exception as err:
                err_msg = "访问数字财政有误, {}".format(str(err).strip())
                print(err_msg)
                time.sleep(2)
        else:
            return False, err_msg


